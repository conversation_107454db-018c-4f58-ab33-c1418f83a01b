import requests
import re
import time
from config.logger import setup_logging
from core.providers.tts.base import TTSProviderBase

TAG = __name__
logger = setup_logging()


class TTSProvider(TTSProviderBase):
    def __init__(self, config, delete_audio_file):
        super().__init__(config, delete_audio_file)
        self.url = config.get("url", "http://127.0.0.1:9880/tts")
        self.text_lang = config.get("text_lang", "zh")
        self.prompt_lang = config.get("prompt_lang", "zh")
        self.media_type = config.get("media_type", "wav")
        self.batch_size = config.get("batch_size", 1)
        self.text_split_method = config.get("text_split_method", "cut5")
        self.streaming_mode = config.get("streaming_mode", False)
        self.audio_file_type = self.media_type

        # 添加情绪状态记忆
        self.current_emotion = "neutral"  # 当前对话的情绪状态
        self.emotion_reset_timeout = 30   # 30秒后重置情绪状态
        self.last_emotion_time = 0        # 上次设置情绪的时间

        # 情绪映射表 - 将各种情绪映射到你的TTS支持的情绪
        self.emotion_map = {
            "happy": "happy",      # 开心
            "laughing": "happy",   # 大笑 -> 开心
            "sad": "sad",          # 悲伤
            "crying": "sad",       # 哭泣 -> 悲伤
            "angry": "angry",      # 愤怒
            "shocked": "fearful",  # 震惊 -> 恐惧
            "surprised": "surprised", # 惊讶
            "neutral": "neutral",  # 中性
            "thinking": "neutral", # 思考 -> 中性
            "relaxed": "neutral",  # 放松 -> 中性
            "sleepy": "neutral",   # 困倦 -> 中性
            "silly": "happy",      # 搞怪 -> 开心
            "confused": "neutral", # 困惑 -> 中性
            "embarrassed": "neutral", # 尴尬 -> 中性
            "winking": "happy",    # 眨眼 -> 开心
            "cool": "neutral",     # 酷 -> 中性
            "delicious": "happy",  # 美味 -> 开心
            "kissy": "happy",      # 亲吻 -> 开心
            "confident": "neutral", # 自信 -> 中性
            "loving": "happy",     # 爱 -> 开心
            "fearful": "fearful",  # 恐惧
            "disgusted": "disgusted", # 厌恶
            "excited": "happy",     # 兴奋 -> 开心
        }

        logger.bind(tag=TAG).info(f"本地自定义TTS初始化完成，URL: {self.url}")

    def extract_emotion_and_text(self, text):
        """从文本中提取情绪标签，支持 [happy] 和 😔 两种格式，并记住情绪状态"""

        current_time = time.time()

        # 首先尝试匹配完整的方括号情绪标签 [emotion]
        bracket_match = re.search(r'\[(\w+)\]', text)
        if bracket_match:
            emotion = bracket_match.group(1).lower()
            # 移除情绪标签
            clean_text = re.sub(r'\[\w+\]', '', text).strip()
            # 直接使用提取的情绪（如果是支持的情绪），否则映射
            if emotion in ["happy", "sad", "angry", "fearful", "neutral", "surprised", "disgusted"]:
                mapped_emotion = emotion
            else:
                mapped_emotion = self.emotion_map.get(emotion, "neutral")

            # 更新当前情绪状态和时间
            self.current_emotion = mapped_emotion
            self.last_emotion_time = current_time

            return mapped_emotion, clean_text

        # 尝试匹配不完整的情绪标签（缺少开头的[）如：happy]文本
        incomplete_match = re.search(r'^(\w+)\]', text)
        if incomplete_match:
            emotion = incomplete_match.group(1).lower()
            # 移除不完整的情绪标签
            clean_text = re.sub(r'^\w+\]', '', text).strip()
            # 直接使用提取的情绪（如果是支持的情绪），否则映射
            if emotion in ["happy", "sad", "angry", "fearful", "neutral", "surprised", "disgusted"]:
                mapped_emotion = emotion
            else:
                mapped_emotion = self.emotion_map.get(emotion, "neutral")

            # 更新当前情绪状态和时间
            self.current_emotion = mapped_emotion
            self.last_emotion_time = current_time

            return mapped_emotion, clean_text

        # 尝试匹配其他可能的格式，如：emotion]文本 或 [emotion文本
        other_patterns = [
            r'^(\w+)\](.*)$',  # emotion]文本
            r'^\[(\w+)(.*)$',  # [emotion文本（缺少结尾]）
        ]

        for pattern in other_patterns:
            match = re.search(pattern, text)
            if match:
                emotion = match.group(1).lower()
                clean_text = match.group(2).strip() if len(match.groups()) > 1 else text
                # 直接使用提取的情绪（如果是支持的情绪），否则映射
                if emotion in ["happy", "sad", "angry", "fearful", "neutral", "surprised", "disgusted"]:
                    mapped_emotion = emotion
                else:
                    mapped_emotion = self.emotion_map.get(emotion, "neutral")

                # 更新当前情绪状态和时间
                self.current_emotion = mapped_emotion
                self.last_emotion_time = current_time

                return mapped_emotion, clean_text

        # 如果没有找到任何情绪标签，检查是否可以继承之前的情绪状态
        if (current_time - self.last_emotion_time) < self.emotion_reset_timeout:
            # 在超时时间内，继承之前的情绪
            logger.bind(tag=TAG).debug(f"继承之前的情绪状态: {self.current_emotion}")
            return self.current_emotion, text
        else:
            # 超时了，重置为中性情绪
            self.current_emotion = "neutral"

        # 如果没有方括号标签，检查开头是否有emoji表情
        if text and len(text) > 0:
            first_char = text[0]
            # emoji到情绪的映射（从项目中的EMOJI_MAP获取）
            emoji_to_emotion = {
                "😂": "laughing",
                "😭": "crying",
                "😠": "angry",
                "😔": "sad",
                "😍": "loving",
                "😲": "surprised",
                "😱": "shocked",
                "🤔": "thinking",
                "😌": "relaxed",
                "😴": "sleepy",
                "😜": "silly",
                "🙄": "confused",
                "😶": "neutral",
                "🙂": "happy",
                "😆": "laughing",
                "😳": "embarrassed",
                "😉": "winking",
                "😎": "cool",
                "🤤": "delicious",
                "😘": "kissy",
                "😏": "confident",
            }

            if first_char in emoji_to_emotion:
                emotion = emoji_to_emotion[first_char]
                # 移除开头的emoji
                clean_text = text[1:].strip()
                # 映射到支持的情绪
                mapped_emotion = self.emotion_map.get(emotion, "neutral")

                # 更新当前情绪状态和时间
                self.current_emotion = mapped_emotion
                self.last_emotion_time = current_time

                return mapped_emotion, clean_text

        # 如果什么都没找到，返回当前情绪状态（可能是继承的）
        return self.current_emotion, text

    async def text_to_speak(self, text, output_file):
        """将文本转换为语音"""
        try:
            # 从文本中提取情绪标签和清理文本
            emotion, clean_text = self.extract_emotion_and_text(text)

            # 检查是否是继承的情绪（没有任何情绪标签的文本）
            has_emotion_tag = (re.search(r'\[(\w+)\]', text) or
                             re.search(r'^(\w+)\]', text) or
                             any(emoji in text for emoji in ["😂", "😭", "😠", "😔", "😍", "😲", "😱", "🤔", "😌", "😴", "😜", "🙄", "😶", "🙂", "😆", "😳", "😉", "😎", "🤤", "😘", "😏"]))

            logger.bind(tag=TAG).info(f"本地TTS处理 - 原始文本: {text}")
            logger.bind(tag=TAG).info(f"本地TTS处理 - 提取的情绪: {emotion} {'(新检测)' if has_emotion_tag else '(继承)'}")
            logger.bind(tag=TAG).info(f"本地TTS处理 - 清理后文本: {clean_text}")

            # 如果检测到不完整的标签，记录警告
            if re.search(r'^(\w+)\]', text) and not re.search(r'\[(\w+)\]', text):
                logger.bind(tag=TAG).warning(f"检测到不完整的情绪标签格式: {text[:20]}... (缺少开头的'[')")

            # 构建请求数据，按照你的API格式，确保格式完整
            tts_text = f"[{emotion}]{clean_text}"

            # 处理换行符 - 你的TTS不支持换行符，需要替换掉
            tts_text = tts_text.replace('\n', '，')  # 将换行符替换为中文逗号

            request_data = {
                "text": tts_text,
                "text_lang": self.text_lang,
                "prompt_lang": self.prompt_lang,
                "media_type": self.media_type,
                "batch_size": self.batch_size,
                "text_split_method": self.text_split_method,
                "streaming_mode": self.streaming_mode
            }

            logger.bind(tag=TAG).info(f"发送给TTS的完整格式: {tts_text}")

            logger.bind(tag=TAG).debug(f"TTS请求数据: {request_data}")

            # 发送请求
            headers = {"Content-Type": "application/json"}
            response = requests.post(
                self.url,
                json=request_data,
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                audio_data = response.content

                if output_file:
                    # 保存到文件
                    with open(output_file, "wb") as f:
                        f.write(audio_data)
                    logger.bind(tag=TAG).info(f"TTS音频已保存到: {output_file}")
                    return output_file
                else:
                    # 直接返回音频数据
                    logger.bind(tag=TAG).info(f"TTS音频生成成功，大小: {len(audio_data)} bytes")
                    return audio_data
            else:
                error_msg = f"本地TTS请求失败: {response.status_code} - {response.text}"
                logger.bind(tag=TAG).error(error_msg)
                raise Exception(error_msg)

        except requests.exceptions.RequestException as e:
            error_msg = f"本地TTS网络请求异常: {str(e)}"
            logger.bind(tag=TAG).error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"本地TTS处理异常: {str(e)}"
            logger.bind(tag=TAG).error(error_msg)
            raise Exception(error_msg)
